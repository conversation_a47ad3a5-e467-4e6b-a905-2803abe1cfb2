
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Users, Plus, Share, Copy, UserMinus } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { useFamilyGroups } from "@/hooks/useFamilyGroups";
import { useFamilyMembers } from "@/hooks/useFamilyMembers";
import { useFamilyInvitations } from "@/hooks/useFamilyInvitations";

export const FamilyGroup = () => {
  const { toast } = useToast();
  const [inviteEmail, setInviteEmail] = useState("");
  const [joinCode, setJoinCode] = useState("");
  const [newGroupName, setNewGroupName] = useState("");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showJoinForm, setShowJoinForm] = useState(false);

  const { familyGroups, createGroup, joinGroup, leaveGroup, isCreatingGroup, isJoiningGroup, isLeavingGroup, error: groupsError } = useFamilyGroups();
  const currentGroup = familyGroups && familyGroups.length > 0 ? familyGroups[0] : null;
  const { familyMembers, error: membersError } = useFamilyMembers(currentGroup?.id);
  const { invitations, sendInvitation, isSendingInvitation, error: invitationsError } = useFamilyInvitations(currentGroup?.id);

  // Show error message if there are database issues
  if (groupsError || membersError || invitationsError) {
    console.error('Database errors:', { groupsError, membersError, invitationsError });
  }

  const handleCreateGroup = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newGroupName.trim()) {
      toast({
        title: "Erro",
        description: "Por favor, insira um nome para o grupo.",
        variant: "destructive"
      });
      return;
    }

    try {
      createGroup(newGroupName);
      setNewGroupName("");
      setShowCreateForm(false);
    } catch (error) {
      console.error('Error creating group:', error);
      toast({
        title: "Erro",
        description: "Erro ao criar grupo. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  const handleJoinGroup = (e: React.FormEvent) => {
    e.preventDefault();
    if (!joinCode.trim()) {
      toast({
        title: "Erro",
        description: "Por favor, insira um código de convite.",
        variant: "destructive"
      });
      return;
    }

    joinGroup(joinCode);
    setJoinCode("");
    setShowJoinForm(false);
  };

  const handleInvite = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inviteEmail) {
      toast({
        title: "Erro",
        description: "Por favor, insira um email válido.",
        variant: "destructive"
      });
      return;
    }

    sendInvitation(inviteEmail);
    setInviteEmail("");
  };

  const handleLeaveGroup = () => {
    if (!currentGroup) return;

    if (window.confirm('Tem certeza que deseja sair deste grupo familiar? Esta ação não pode ser desfeita.')) {
      leaveGroup(currentGroup.id);
    }
  };

  const copyShareCode = () => {
    if (currentGroup?.invite_code) {
      navigator.clipboard.writeText(currentGroup.invite_code);
      toast({
        title: "Código Copiado!",
        description: "O código de compartilhamento foi copiado para a área de transferência.",
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <Users className="w-6 h-6 text-green-600" />
        <h2 className="text-xl font-semibold text-gray-800">Grupo Familiar</h2>
      </div>

      {!currentGroup ? (
        <div className="space-y-4">
          <Card className="bg-white shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-800">Você não está em nenhum grupo</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Button onClick={() => setShowCreateForm(true)} className="bg-green-600 hover:bg-green-700 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  Criar Grupo
                </Button>
                <Button onClick={() => setShowJoinForm(true)} variant="outline">
                  Entrar em Grupo
                </Button>
              </div>
              
              {showCreateForm && (
                <form onSubmit={handleCreateGroup} className="space-y-4 p-4 bg-gray-50 rounded-lg">
                  <div className="space-y-2">
                    <Label htmlFor="groupName">Nome do Grupo</Label>
                    <Input
                      id="groupName"
                      value={newGroupName}
                      onChange={(e) => setNewGroupName(e.target.value)}
                      placeholder="Família Silva"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button type="submit" disabled={isCreatingGroup}>
                      {isCreatingGroup ? "Criando..." : "Criar"}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => setShowCreateForm(false)}>
                      Cancelar
                    </Button>
                  </div>
                </form>
              )}

              {showJoinForm && (
                <form onSubmit={handleJoinGroup} className="space-y-4 p-4 bg-gray-50 rounded-lg">
                  <div className="space-y-2">
                    <Label htmlFor="joinCode">Código do Grupo</Label>
                    <Input
                      id="joinCode"
                      value={joinCode}
                      onChange={(e) => setJoinCode(e.target.value)}
                      placeholder="ABC12345"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button type="submit" disabled={isJoiningGroup}>
                      {isJoiningGroup ? "Entrando..." : "Entrar"}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => setShowJoinForm(false)}>
                      Cancelar
                    </Button>
                  </div>
                </form>
              )}
            </CardContent>
          </Card>
        </div>
      ) : (
        <>
          {/* Share Code */}
          <Card className="bg-green-50 border-green-200">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg text-green-800 flex items-center gap-2">
                  <Share className="w-5 h-5" />
                  Código de Compartilhamento - {currentGroup.name}
                </CardTitle>
                <Button
                  onClick={handleLeaveGroup}
                  disabled={isLeavingGroup}
                  variant="outline"
                  size="sm"
                  className="text-red-600 border-red-300 hover:bg-red-50"
                >
                  <UserMinus className="w-4 h-4 mr-2" />
                  {isLeavingGroup ? 'Saindo...' : 'Sair do Grupo'}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <div className="flex-1 bg-white p-3 rounded-lg border font-mono text-lg tracking-wider">
                  {currentGroup.invite_code}
                </div>
                <Button onClick={copyShareCode} variant="outline">
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
              <p className="text-sm text-green-700 mt-2">
                Compartilhe este código com sua família para que eles possam acessar a despensa e lista de compras.
              </p>
            </CardContent>
          </Card>

          {/* Invite New Member */}
          <Card className="bg-white shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-800">Convidar Membro</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleInvite} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email do familiar</Label>
                  <Input
                    id="email"
                    type="email"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="bg-white"
                  />
                </div>
                <Button type="submit" disabled={isSendingInvitation} className="bg-green-600 hover:bg-green-700 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  {isSendingInvitation ? "Enviando..." : "Enviar Convite"}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Family Members */}
          <Card className="bg-white shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-800">
                Outros Membros da Família ({familyMembers?.length || 0})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {familyMembers && familyMembers.length > 0 ? familyMembers.map((member) => (
                  <div key={member.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Avatar className="w-10 h-10">
                        <AvatarFallback className="bg-green-100 text-green-700">
                          {member.profiles?.full_name?.split(' ').map((n: string) => n[0]).join('') || 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-medium text-gray-800">{member.profiles?.full_name || 'Usuário'}</h3>
                        <p className="text-sm text-gray-600">{member.profiles?.email}</p>
                        <p className="text-xs text-gray-500">
                          Membro desde {new Date(member.joined_at).toLocaleDateString('pt-BR')}
                        </p>
                      </div>
                    </div>
                    <Badge variant={member.role === 'admin' ? 'default' : 'secondary'}>
                      {member.role === 'admin' ? 'Administrador' : 'Membro'}
                    </Badge>
                  </div>
                )) : (
                  <div className="text-center py-4 text-gray-500">
                    <p>Você é o único membro deste grupo familiar</p>
                    <p className="text-sm mt-1">Convide outros membros usando o código de convite acima</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Pending Invitations */}
          {invitations && invitations.length > 0 && (
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-gray-800">
                  Convites Pendentes ({invitations.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {invitations.map((invitation) => (
                    <div key={invitation.id} className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                      <div>
                        <p className="font-medium text-gray-800">{invitation.email}</p>
                        <p className="text-sm text-gray-600">
                          Enviado em {new Date(invitation.created_at).toLocaleDateString('pt-BR')}
                        </p>
                      </div>
                      <Badge variant="outline" className="text-yellow-700 border-yellow-300">
                        {invitation.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Instructions */}
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader>
              <CardTitle className="text-lg text-blue-800">Como funciona a colaboração?</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-blue-700">
                <li>• Todos os membros podem ver e editar a despensa</li>
                <li>• Alterações são sincronizadas em tempo real</li>
                <li>• Qualquer membro pode adicionar itens à lista de compras</li>
                <li>• Administradores podem convidar novos membros</li>
                <li>• Todos recebem notificações de itens vencendo</li>
              </ul>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};
