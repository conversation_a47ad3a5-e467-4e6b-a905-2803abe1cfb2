import { Badge } from "@/components/ui/badge";
import { Users, User } from "lucide-react";

interface FilterStatsProps {
  itemStats: {
    total: number;
    myItems: number;
    familyItems: number;
  };
  selectedUserId: string;
  className?: string;
}

export const FilterStats = ({ itemStats, selectedUserId, className = "" }: FilterStatsProps) => {
  if (itemStats.total === 0) return null;

  const getStatsText = () => {
    if (selectedUserId === 'all') {
      return (
        <div className="flex items-center gap-2 text-xs text-gray-600">
          <div className="flex items-center gap-1">
            <User className="w-3 h-3" />
            <span>Seus: {itemStats.myItems}</span>
          </div>
          <div className="flex items-center gap-1">
            <Users className="w-3 h-3" />
            <span>Família: {itemStats.familyItems}</span>
          </div>
          <div className="flex items-center gap-1">
            <span>Total: {itemStats.total}</span>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <div className={`flex items-center justify-center py-2 ${className}`}>
      {getStatsText()}
    </div>
  );
};
