
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { FamilyMember } from '@/types';
import { useToast } from './use-toast';

export const useFamilyMembers = (familyGroupId?: string) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: familyMembers = [], isLoading, error } = useQuery({
    queryKey: ['familyMembers', familyGroupId],
    queryFn: async () => {
      if (!familyGroupId) {
        return [];
      }

      // First, get family members
      const { data: members, error: membersError } = await supabase
        .from('family_members')
        .select('*')
        .eq('family_group_id', familyGroupId)
        .order('joined_at', { ascending: true });

      if (membersError) {
        console.error('Error fetching family members:', membersError);
        throw membersError;
      }

      if (!members || members.length === 0) {
        return [];
      }

      // Then, get profiles for those members
      const userIds = members.map(member => member.user_id);
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id, full_name, email, avatar_url')
        .in('id', userIds);

      if (profilesError) {
        console.error('Error fetching profiles:', profilesError);
        // Don't throw error, just return members without profile data
      }

      // Combine members with their profiles and filter out current user
      const membersWithProfiles = members
        .filter(member => member.user_id !== user?.id) // Exclude current user
        .map(member => ({
          ...member,
          profiles: profiles?.find(profile => profile.id === member.user_id) || null
        }));

      return membersWithProfiles as (FamilyMember & { profiles: any })[];
    },
    enabled: !!familyGroupId,
  });

  const removeMemberMutation = useMutation({
    mutationFn: async (memberId: string) => {
      const { error } = await supabase
        .from('family_members')
        .delete()
        .eq('id', memberId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['familyMembers'] });
      toast({
        title: "Membro removido",
        description: "Membro removido do grupo com sucesso.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: "Erro ao remover membro.",
        variant: "destructive",
      });
      console.error('Error removing family member:', error);
    },
  });

  const updateRoleMutation = useMutation({
    mutationFn: async ({ memberId, role }: { memberId: string, role: 'admin' | 'member' }) => {
      const { error } = await supabase
        .from('family_members')
        .update({ role })
        .eq('id', memberId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['familyMembers'] });
      toast({
        title: "Função atualizada",
        description: "Função do membro atualizada com sucesso.",
      });
    },
    onError: (error) => {
      toast({
        title: "Erro",
        description: "Erro ao atualizar função.",
        variant: "destructive",
      });
      console.error('Error updating member role:', error);
    },
  });

  // Get current user's role in the group
  const currentUserMember = familyMembers.find(member => member.user_id === user?.id);
  const isAdmin = currentUserMember?.role === 'admin';

  return {
    familyMembers,
    isLoading,
    error,
    isAdmin,
    currentUserMember,
    removeMember: removeMemberMutation.mutate,
    updateRole: updateRoleMutation.mutate,
    isRemovingMember: removeMemberMutation.isPending,
    isUpdatingRole: updateRoleMutation.isPending,
  };
};
