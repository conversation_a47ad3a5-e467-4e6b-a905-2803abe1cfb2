-- Drop the restrictive policy
DROP POLICY IF EXISTS "family_members_select_own" ON public.family_members;

-- Create a more permissive policy for viewing family members
CREATE POLICY "Users can view all members in their family groups" ON public.family_members
FOR SELECT USING (
  -- User can see their own records
  user_id = auth.uid() OR
  -- User can see records of members in the same family groups
  family_group_id IN (
    SELECT family_group_id FROM public.family_members 
    WHERE user_id = auth.uid()
  )
);
