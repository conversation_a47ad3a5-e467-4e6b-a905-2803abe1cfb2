
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertTriangle, Calendar, Trash2, Edit3, Plus, Minus, Grid3X3, List, X, User } from "lucide-react";
import { PantryItem } from "@/types";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { EditItemForm } from "./EditItemForm";
import { AddItemForm } from "./AddItemForm";
import { UserFilter } from "./UserFilter";
import { FilterStats } from "./FilterStats";
import { useState } from "react";
import { useAppContext } from "@/contexts/AppContext";
import { useUserFilter } from "@/hooks/useUserFilter";

interface PantryDashboardProps {
  items: PantryItem[];
  onUpdateItem: (id: string, updates: Partial<PantryItem>) => void;
  onDeleteItem: (id: string) => void;
  onAddItem: (item: Omit<PantryItem, 'id' | 'addedDate'>) => void;
}

export const PantryDashboard = ({ items, onUpdateItem, onDeleteItem, onAddItem }: PantryDashboardProps) => {
  const { showAddPantryForm, setShowAddPantryForm } = useAppContext();
  const [editingItem, setEditingItem] = useState<PantryItem | null>(null);
  const [activeCategory, setActiveCategory] = useState<string>("Todos");
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');

  // User filter hook
  const {
    selectedUserId,
    setSelectedUserId,
    showOwnerInfo,
    filteredItems: userFilteredItems,
    getItemOwnerInfo,
    itemStats,
    toggleOwnerInfo
  } = useUserFilter(items);

  const getExpiryStatus = (expiryDate: string) => {
    const today = new Date();
    const expiry = new Date(expiryDate);
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysUntilExpiry < 0) return { status: 'expired', color: 'destructive', days: Math.abs(daysUntilExpiry) };
    if (daysUntilExpiry <= 3) return { status: 'expiring', color: 'destructive', days: daysUntilExpiry };
    if (daysUntilExpiry <= 7) return { status: 'warning', color: 'secondary', days: daysUntilExpiry };
    return { status: 'good', color: 'default', days: daysUntilExpiry };
  };

  const categories = ["Todos", ...Array.from(new Set(userFilteredItems.map(item => item.category)))];
  const filteredItems = activeCategory === "Todos" ? userFilteredItems : userFilteredItems.filter(item => item.category === activeCategory);

  const lowStockItems = userFilteredItems.filter(item => item.isLowStock);
  const expiringItems = userFilteredItems.filter(item => {
    const status = getExpiryStatus(item.expiryDate);
    return status.status === 'expiring' || status.status === 'expired';
  });

  const updateQuantity = (itemId: string, change: number) => {
    const item = items.find(i => i.id === itemId);
    if (item) {
      const newQuantity = Math.max(0, item.quantity + change);
      onUpdateItem(itemId, { 
        quantity: newQuantity,
        isLowStock: newQuantity <= 2
      });
    }
  };

  // Generate mobile alerts
  const mobileAlerts = [
    ...lowStockItems.slice(0, 2).map(item => ({
      id: `low-${item.id}`,
      type: 'lowStock' as const,
      title: 'Estoque baixo',
      message: `${item.name} - ${item.quantity} ${item.unit}`,
      priority: 'high' as const,
      itemId: item.id
    })),
    ...expiringItems.slice(0, 2).map(item => {
      const status = getExpiryStatus(item.expiryDate);
      return {
        id: `exp-${item.id}`,
        type: status.status === 'expired' ? 'expired' as const : 'expiring' as const,
        title: status.status === 'expired' ? 'Produto vencido' : 'Produto vencendo',
        message: `${item.name} - ${status.status === 'expired' ? `vencido há ${status.days} dias` : `vence em ${status.days} dias`}`,
        priority: status.status === 'expired' ? 'high' as const : 'medium' as const,
        itemId: item.id
      };
    })
  ];

  const handleQuickAction = (action: string, itemId?: string) => {
    console.log('Quick action:', action, 'for item:', itemId);
    // Implement quick actions here
  };

  const handleDismissAlert = (alertId: string) => {
    console.log('Dismiss alert:', alertId);
    // Implement alert dismissal
  };

  return (
    <div className="space-y-6 overflow-x-hidden">
      {/* Add Item Button */}
      <div className="flex justify-end">
        
      </div>
      {/* Mobile Summary Cards - Horizontal Layout */}
      <div className="grid grid-cols-3 sm:grid-cols-1 md:grid-cols-3 gap-2 sm:gap-3">
        <Card className="bg-gradient-to-r from-green-50 to-emerald-50 shadow-sm border-green-200">
          <CardContent className="p-3 sm:p-4">
            <div className="flex flex-col sm:flex-row items-center sm:justify-between gap-2 sm:gap-0">
              <div className="text-center sm:text-left">
                <p className="text-xs sm:text-sm font-medium text-green-700">Total</p>
                <p className="text-lg sm:text-2xl font-bold text-green-800">{filteredItems.length}</p>
              </div>
              <div className="w-8 h-8 sm:w-12 sm:h-12 bg-green-100 rounded-full flex items-center justify-center">
                <Grid3X3 className="w-4 h-4 sm:w-6 sm:h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-red-50 to-pink-50 shadow-sm border-red-200">
          <CardContent className="p-3 sm:p-4">
            <div className="flex flex-col sm:flex-row items-center sm:justify-between gap-2 sm:gap-0">
              <div className="text-center sm:text-left">
                <p className="text-xs sm:text-sm font-medium text-red-700">Baixo</p>
                <p className="text-lg sm:text-2xl font-bold text-red-800">{lowStockItems.length}</p>
              </div>
              <div className="w-8 h-8 sm:w-12 sm:h-12 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-4 h-4 sm:w-6 sm:h-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-50 to-yellow-50 shadow-sm border-orange-200">
          <CardContent className="p-3 sm:p-4">
            <div className="flex flex-col sm:flex-row items-center sm:justify-between gap-2 sm:gap-0">
              <div className="text-center sm:text-left">
                <p className="text-xs sm:text-sm font-medium text-orange-700">Vencendo</p>
                <p className="text-lg sm:text-2xl font-bold text-orange-800">{expiringItems.length}</p>
              </div>
              <div className="w-8 h-8 sm:w-12 sm:h-12 bg-orange-100 rounded-full flex items-center justify-center">
                <Calendar className="w-4 h-4 sm:w-6 sm:h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Mobile Alerts Section - Removido conforme solicitação */}

      {/* User Filter */}
      <UserFilter
        selectedUserId={selectedUserId}
        onUserChange={setSelectedUserId}
        showOwnerInfo={showOwnerInfo}
        onToggleOwnerInfo={toggleOwnerInfo}
      />

      {/* Category Carousel for Mobile */}
      <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-hide">
        {categories.map((category) => (
          <Button
            key={category}
            variant={activeCategory === category ? "default" : "outline"}
            size="sm"
            onClick={() => setActiveCategory(category)}
            className="whitespace-nowrap min-w-fit text-xs sm:text-sm"
          >
            {category}
          </Button>
        ))}
      </div>

      {/* Filter Stats */}
      <FilterStats
        itemStats={itemStats}
        selectedUserId={selectedUserId}
        className="border-t border-gray-200 mt-2"
      />

      {/* View Mode Toggle (Mobile) */}
      <div className="flex items-center justify-between sm:hidden">
        <h3 className="text-base sm:text-lg font-semibold text-gray-800">
          {activeCategory} ({filteredItems.length})
        </h3>
        <div className="flex rounded-lg border">
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
            className="rounded-r-none h-8 w-8 p-0"
          >
            <List className="w-3 h-3" />
          </Button>
          <Button
            variant={viewMode === 'grid' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('grid')}
            className="rounded-l-none h-8 w-8 p-0"
          >
            <Grid3X3 className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {/* Items - Optimized for Mobile */}
      <div className={viewMode === 'grid' 
        ? "grid grid-cols-1 sm:grid-cols-2 gap-3" 
        : "space-y-3"
      }>
        {filteredItems.map((item) => {
          const expiryStatus = getExpiryStatus(item.expiryDate);
          const ownerInfo = getItemOwnerInfo(item);
          return (
            <Card key={item.id} className="bg-white shadow-sm border-l-4 border-l-green-400 active:scale-[0.98] transition-transform">
              <CardContent className="p-3 sm:p-4">
                <div className="space-y-3">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-semibold text-gray-800 text-sm sm:text-base truncate">{item.name}</h3>
                        {showOwnerInfo && (
                          <Badge
                            variant={ownerInfo.isCurrentUser ? "default" : "secondary"}
                            className="text-xs flex items-center gap-1"
                          >
                            <User className="w-3 h-3" />
                            {ownerInfo.ownerLabel}
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-1 sm:gap-2 mt-1 flex-wrap">
                        <Badge variant="outline" className="text-xs">
                          {item.category}
                        </Badge>
                        {item.isLowStock && (
                          <Badge variant="destructive" className="text-xs">
                            Baixo
                          </Badge>
                        )}
                        {expiryStatus.status !== 'good' && (
                          <Badge variant={expiryStatus.color as any} className="text-xs">
                            {expiryStatus.status === 'expired' 
                              ? `Vencido`
                              : `${expiryStatus.days}d`
                            }
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Quantity Controls */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateQuantity(item.id, -1)}
                        disabled={item.quantity <= 0}
                        className="h-7 w-7 sm:h-8 sm:w-8 p-0"
                      >
                        <Minus className="w-3 h-3" />
                      </Button>
                      <span className="font-semibold text-gray-700 min-w-[50px] sm:min-w-[60px] text-center text-sm">
                        {item.quantity} {item.unit}
                      </span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateQuantity(item.id, 1)}
                        className="h-7 w-7 sm:h-8 sm:w-8 p-0"
                      >
                        <Plus className="w-3 h-3" />
                      </Button>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => setEditingItem(item)}
                            className="h-7 w-7 sm:h-8 sm:w-8 p-0"
                          >
                            <Edit3 className="w-3 h-3 sm:w-4 sm:h-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Editar Item</DialogTitle>
                          </DialogHeader>
                          {editingItem && (
                            <EditItemForm 
                              item={editingItem}
                              onUpdateItem={onUpdateItem}
                              onClose={() => setEditingItem(null)}
                            />
                          )}
                        </DialogContent>
                      </Dialog>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => onDeleteItem(item.id)}
                        className="text-red-600 hover:text-red-700 h-7 w-7 sm:h-8 sm:w-8 p-0"
                      >
                        <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {/* Expiry Date */}
                  <div className="text-xs text-gray-500 flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    Vence: {new Date(item.expiryDate).toLocaleDateString('pt-BR')}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredItems.length === 0 && (
        <Card className="bg-white shadow-sm">
          <CardContent className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Calendar className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-600 mb-2">Nenhum item encontrado</h3>
            <p className="text-gray-500">
              {activeCategory === "Todos" 
                ? "Adicione itens à sua despensa para começar" 
                : `Nenhum item na categoria "${activeCategory}"`
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Add Pantry Item Form */}
      {showAddPantryForm && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={(e) => {
            // Fechar modal ao clicar no backdrop
            if (e.target === e.currentTarget) {
              setShowAddPantryForm(false);
            }
          }}
        >
          <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto relative">
            {/* Botão de fechar no canto superior direito */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAddPantryForm(false)}
              className="absolute top-4 right-4 h-8 w-8 p-0 hover:bg-gray-100 z-10"
            >
              <X className="h-4 w-4" />
            </Button>

            <div className="pr-10 mb-4">
              <h2 className="text-xl font-bold">Adicionar Item à Despensa</h2>
            </div>

            <AddItemForm
              onAddItem={(item) => {
                onAddItem(item);
                setShowAddPantryForm(false);
              }}
              showCard={false}
            />
          </div>
        </div>
      )}
    </div>
  );
};
