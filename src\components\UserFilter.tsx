import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Users, User, Eye, EyeOff } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useFamilyMembers } from "@/hooks/useFamilyMembers";
import { useFamilyGroups } from "@/hooks/useFamilyGroups";

interface UserFilterProps {
  selectedUserId: string;
  onUserChange: (userId: string) => void;
  showOwnerInfo?: boolean;
  onToggleOwnerInfo?: () => void;
}

export const UserFilter = ({ 
  selectedUserId, 
  onUserChange, 
  showOwnerInfo = false, 
  onToggleOwnerInfo 
}: UserFilterProps) => {
  const { user } = useAuth();
  const { familyGroups } = useFamilyGroups();
  const currentGroup = familyGroups && familyGroups.length > 0 ? familyGroups[0] : null;
  const { familyMembers } = useFamilyMembers(currentGroup?.id);

  // Create user options
  const userOptions = [
    { id: 'all', name: 'Todos os itens', email: '', isCurrentUser: false },
    { id: user?.id || '', name: 'Meus itens', email: user?.email || '', isCurrentUser: true },
  ];

  // Add family members (excluding current user)
  if (familyMembers) {
    familyMembers.forEach(member => {
      if (member.user_id !== user?.id) {
        userOptions.push({
          id: member.user_id,
          name: member.profiles?.full_name || member.profiles?.email || 'Membro da família',
          email: member.profiles?.email || '',
          isCurrentUser: false,
        });
      }
    });
  }

  const selectedOption = userOptions.find(option => option.id === selectedUserId);
  const isInFamily = familyMembers && familyMembers.length > 1;

  if (!isInFamily) {
    // If not in a family, don't show the filter
    return null;
  }

  return (
    <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-center justify-between mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
      <div className="flex items-center gap-2 flex-1">
        <Users className="w-4 h-4 text-blue-600" />
        <span className="text-sm font-medium text-blue-800">Visualizar itens de:</span>
        
        <Select value={selectedUserId} onValueChange={onUserChange}>
          <SelectTrigger className="w-48 bg-white">
            <SelectValue placeholder="Selecionar usuário" />
          </SelectTrigger>
          <SelectContent>
            {userOptions.map((option) => (
              <SelectItem key={option.id} value={option.id}>
                <div className="flex items-center gap-2">
                  {option.id === 'all' ? (
                    <Users className="w-4 h-4" />
                  ) : (
                    <User className="w-4 h-4" />
                  )}
                  <span>{option.name}</span>
                  {option.isCurrentUser && (
                    <Badge variant="secondary" className="text-xs">Você</Badge>
                  )}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {onToggleOwnerInfo && (
        <Button
          variant="outline"
          size="sm"
          onClick={onToggleOwnerInfo}
          className="flex items-center gap-2"
        >
          {showOwnerInfo ? (
            <>
              <EyeOff className="w-4 h-4" />
              Ocultar donos
            </>
          ) : (
            <>
              <Eye className="w-4 h-4" />
              Mostrar donos
            </>
          )}
        </Button>
      )}

      {selectedOption && selectedOption.id !== 'all' && (
        <div className="text-xs text-blue-600">
          {selectedOption.id === user?.id ? (
            <Badge variant="default" className="bg-green-100 text-green-800">
              Seus itens
            </Badge>
          ) : (
            <Badge variant="outline" className="border-blue-300 text-blue-700">
              Itens de {selectedOption.name}
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};
