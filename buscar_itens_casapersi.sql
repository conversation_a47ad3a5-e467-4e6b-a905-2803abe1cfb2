-- <PERSON><PERSON>t para buscar quantos itens a casapersi tem na despensa
-- Execute este script no Supabase SQL Editor

-- 1. <PERSON><PERSON>, vamos encontrar o usuário "casapersi"
SELECT 
  id,
  email,
  full_name,
  created_at
FROM profiles 
WHERE 
  email ILIKE '%casapersi%' OR 
  email ILIKE '%fabiopersi%' OR
  full_name ILIKE '%casapersi%' OR
  full_name ILIKE '%fabio%'
ORDER BY created_at;

-- 2. <PERSON><PERSON> itens da despensa por email específico
WITH casapersi_user AS (
  SELECT id 
  FROM profiles 
  WHERE email ILIKE '%casapersi%' OR email ILIKE '%fabiopersi%'
  LIMIT 1
)
SELECT 
  COUNT(*) as total_itens_despensa,
  p.email as usuario_email,
  p.full_name as nome_completo
FROM pantry_items pi
JOIN profiles p ON pi.user_id = p.id
WHERE pi.user_id IN (SELECT id FROM casapersi_user)
GROUP BY p.email, p.full_name;

-- 3. Detalhes dos itens da despensa da casapersi
WITH casapersi_user AS (
  SELECT id 
  FROM profiles 
  WHERE email ILIKE '%casapersi%' OR email ILIKE '%fabiopersi%'
  LIMIT 1
)
SELECT 
  pi.name as item_nome,
  pi.quantity as quantidade,
  pi.unit as unidade,
  pi.category as categoria,
  pi.expiry_date as data_vencimento,
  pi.is_low_stock as estoque_baixo,
  pi.created_at as adicionado_em,
  p.email as proprietario_email
FROM pantry_items pi
JOIN profiles p ON pi.user_id = p.id
WHERE pi.user_id IN (SELECT id FROM casapersi_user)
ORDER BY pi.created_at DESC;

-- 4. Resumo por categoria
WITH casapersi_user AS (
  SELECT id 
  FROM profiles 
  WHERE email ILIKE '%casapersi%' OR email ILIKE '%fabiopersi%'
  LIMIT 1
)
SELECT 
  pi.category as categoria,
  COUNT(*) as quantidade_itens,
  SUM(pi.quantity) as quantidade_total,
  p.email as usuario
FROM pantry_items pi
JOIN profiles p ON pi.user_id = p.id
WHERE pi.user_id IN (SELECT id FROM casapersi_user)
GROUP BY pi.category, p.email
ORDER BY quantidade_itens DESC;

-- 5. Verificar se casapersi está em algum grupo familiar
WITH casapersi_user AS (
  SELECT id 
  FROM profiles 
  WHERE email ILIKE '%casapersi%' OR email ILIKE '%fabiopersi%'
  LIMIT 1
)
SELECT 
  fg.name as nome_grupo,
  fg.invite_code as codigo_convite,
  fm.role as papel_no_grupo,
  fm.joined_at as entrou_em,
  p.email as usuario_email
FROM family_members fm
JOIN family_groups fg ON fm.family_group_id = fg.id
JOIN profiles p ON fm.user_id = p.id
WHERE fm.user_id IN (SELECT id FROM casapersi_user);

-- 6. Se não encontrar por casapersi, buscar todos os usuários disponíveis
SELECT 
  'Todos os usuários disponíveis:' as info,
  id,
  email,
  full_name,
  created_at
FROM profiles 
ORDER BY created_at DESC;

-- 7. Contagem total de itens por usuário (para comparação)
SELECT 
  p.email,
  p.full_name,
  COUNT(pi.id) as total_itens_despensa
FROM profiles p
LEFT JOIN pantry_items pi ON p.id = pi.user_id
GROUP BY p.id, p.email, p.full_name
HAVING COUNT(pi.id) > 0
ORDER BY total_itens_despensa DESC;
