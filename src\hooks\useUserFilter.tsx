import { useState, useMemo } from 'react';
import { PantryItem, ShoppingItem } from '@/types';
import { useAuth } from './useAuth';

export const useUserFilter = <T extends PantryItem | ShoppingItem>(items: T[]) => {
  const { user } = useAuth();
  const [selectedUserId, setSelectedUserId] = useState<string>('all');
  const [showOwnerInfo, setShowOwnerInfo] = useState<boolean>(false);

  const filteredItems = useMemo(() => {
    if (selectedUserId === 'all') {
      return items;
    }
    
    return items.filter(item => {
      // For PantryItem, check addedById
      if ('addedById' in item) {
        return item.addedById === selectedUserId;
      }
      // For ShoppingItem, check addedBy
      if ('addedBy' in item) {
        return item.addedBy === selectedUserId;
      }
      return false;
    });
  }, [items, selectedUserId]);

  const getItemOwnerInfo = (item: T) => {
    const ownerId = 'addedById' in item ? item.addedById : item.addedBy;
    const isCurrentUser = ownerId === user?.id;
    
    return {
      ownerId,
      isCurrentUser,
      ownerLabel: isCurrentUser ? 'Você' : 'Família'
    };
  };

  const itemStats = useMemo(() => {
    const total = items.length;
    const myItems = items.filter(item => {
      const ownerId = 'addedById' in item ? item.addedById : item.addedBy;
      return ownerId === user?.id;
    }).length;
    const familyItems = total - myItems;

    return {
      total,
      myItems,
      familyItems
    };
  }, [items, user?.id]);

  return {
    selectedUserId,
    setSelectedUserId,
    showOwnerInfo,
    setShowOwnerInfo,
    filteredItems,
    getItemOwnerInfo,
    itemStats,
    toggleOwnerInfo: () => setShowOwnerInfo(prev => !prev)
  };
};
