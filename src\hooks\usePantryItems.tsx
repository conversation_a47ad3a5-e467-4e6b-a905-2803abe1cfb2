import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { PantryItem, DatabasePantryItem } from '@/types';
import { convertDbPantryItem, convertToPantryDbFormat } from '@/utils/database';
import { useToast } from './use-toast';

export const usePantryItems = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: pantryItems = [], isLoading, error } = useQuery({
    queryKey: ['pantryItems', user?.id],
    queryFn: async () => {
      if (!user) return [];
      
      // Simplified query without joins to test RLS policies
      const { data, error } = await supabase
        .from('pantry_items')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching pantry items:', error);
        throw error;
      }

      // Configure realtime listener for family sharing
      const channel = supabase
        .channel('pantry_items_changes')
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'pantry_items'
          // No filter - let RLS handle what the user can see
        }, (payload) => {
          console.log('🔄 Pantry items realtime update:', payload);
          queryClient.invalidateQueries({ queryKey: ['pantryItems', user.id] });
        })
        .subscribe();

      // Cleanup function
      const unsubscribe = () => {
        supabase.removeChannel(channel);
      };

      // Ensure cleanup when component unmounts
      window.addEventListener('beforeunload', unsubscribe);

      return data.map(convertDbPantryItem);
    },
    enabled: !!user,
    retry: 3,
    staleTime: 0, // Force fresh data always
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });

  // Optimistic updates for better UX
  const addItemMutation = useMutation({
    mutationFn: async (item: Omit<PantryItem, 'id' | 'addedDate' | 'addedById'>) => {
      if (!user) throw new Error('User not authenticated');
      
      const dbItem = convertToPantryDbFormat(item, user.id);
      const { data, error } = await supabase
        .from('pantry_items')
        .insert(dbItem)
        .select()
        .single();

      if (error) throw error;
      return convertDbPantryItem(data);
    },
    onMutate: async (newItem) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['pantryItems'] });

      // Snapshot previous value
      const previousItems = queryClient.getQueryData(['pantryItems', user?.id]);

      // Optimistically update cache
      const optimisticItem: PantryItem = {
        id: 'temp-' + Date.now(),
        ...newItem,
        addedDate: new Date().toISOString().split('T')[0],
        addedById: user?.id || '',
      };

      queryClient.setQueryData(['pantryItems', user?.id], (old: PantryItem[] = []) => [
        optimisticItem,
        ...old,
      ]);

      return { previousItems };
    },
    onError: (err, newItem, context) => {
      // Rollback on error
      queryClient.setQueryData(['pantryItems', user?.id], context?.previousItems);
      toast({
        title: "Erro",
        description: "Erro ao adicionar item à despensa.",
        variant: "destructive",
      });
      console.error('Error adding pantry item:', err);
    },
    onSuccess: () => {
      // Force immediate cache invalidation and refetch
      queryClient.invalidateQueries({ queryKey: ['pantryItems', user?.id] });
      queryClient.refetchQueries({ queryKey: ['pantryItems', user?.id], type: 'active' });

      toast({
        title: "Item adicionado",
        description: "Item adicionado à despensa com sucesso.",
      });
    },
    onSettled: () => {
      // Double-check invalidation
      queryClient.invalidateQueries({ queryKey: ['pantryItems', user?.id] });
    },
  });

  const updateItemMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string, updates: Partial<PantryItem> }) => {
      const { data, error } = await supabase
        .from('pantry_items')
        .update({
          name: updates.name,
          quantity: updates.quantity,
          unit: updates.unit,
          category: updates.category,
          expiry_date: updates.expiryDate || null,
          is_low_stock: updates.isLowStock,
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return convertDbPantryItem(data);
    },
    onMutate: async ({ id, updates }) => {
      await queryClient.cancelQueries({ queryKey: ['pantryItems'] });
      const previousItems = queryClient.getQueryData(['pantryItems', user?.id]);

      queryClient.setQueryData(['pantryItems', user?.id], (old: PantryItem[] = []) => 
        old.map(item => item.id === id ? { ...item, ...updates } : item)
      );

      return { previousItems };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(['pantryItems', user?.id], context?.previousItems);
      toast({
        title: "Erro",
        description: "Erro ao atualizar item.",
        variant: "destructive",
      });
      console.error('Error updating pantry item:', err);
    },
    onSuccess: () => {
      toast({
        title: "Item atualizado",
        description: "Item atualizado com sucesso.",
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['pantryItems', user?.id] });
    },
  });

  const deleteItemMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('pantry_items')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onMutate: async (deletedId) => {
      await queryClient.cancelQueries({ queryKey: ['pantryItems'] });
      const previousItems = queryClient.getQueryData(['pantryItems', user?.id]);

      queryClient.setQueryData(['pantryItems', user?.id], (old: PantryItem[] = []) => 
        old.filter(item => item.id !== deletedId)
      );

      return { previousItems };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(['pantryItems', user?.id], context?.previousItems);
      toast({
        title: "Erro",
        description: "Erro ao remover item.",
        variant: "destructive",
      });
      console.error('Error deleting pantry item:', err);
    },
    onSuccess: () => {
      toast({
        title: "Item removido",
        description: "Item removido da despensa com sucesso.",
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['pantryItems', user?.id] });
    },
  });

  return {
    pantryItems,
    isLoading,
    error,
    addItem: addItemMutation.mutate,
    addItemAsync: addItemMutation.mutateAsync,
    updateItem: updateItemMutation.mutate,
    deleteItem: deleteItemMutation.mutate,
    isAddingItem: addItemMutation.isPending,
    isUpdatingItem: updateItemMutation.isPending,
    isDeletingItem: deleteItemMutation.isPending,
  };
};
